# Penambahan Kolom Catatan Simulator pada DataTable

## Perubahan yang Diterapkan

### 1. Perubahan Model (LaporanRadiasiEksternaModel.php)

#### Update Column Order dan Search
- Menambahkan `catatan_simulator` ke dalam `$column_order` dan `$column_search`
- <PERSON><PERSON><PERSON> kolom sekarang: No, <PERSON><PERSON>, Catatan Simulator, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

#### Update Query dengan JOIN
Menambahkan JOIN ke dua tabel simulator:
- **CT Simulator**: `medis.tb_ctSimulatorDokter` (category = "1")
- **Simulator Konvensional**: `medis.tb_simulatorInformationDr` (category = "2")

#### SELECT Statement dengan CASE
```sql
CASE 
    WHEN medis.radiasi_eksterna.category = "1" THEN CONCAT("CT - ", medis.tb_ctSimulatorDokter.deskripsi)
    WHEN medis.radiasi_eksterna.category = "2" THEN CONCAT("SK - ", medis.tb_simulatorInformationDr.diagnosa)
    ELSE "Tidak ada catatan"
END as catatan_simulator
```

### 2. Perubahan Controller (LaporanRadiasiEksterna.php)

#### Update Method get_data()
- Menambahkan `$field->catatan_simulator` ke dalam array row
- Urutan data sekarang: No, Lokasi, Catatan Simulator, Oleh, Waktu, Aksi

### 3. Perubahan View (laporan_tindakan_radiasi_eksterna.php)

#### Update Table Header
- Menambahkan kolom "Catatan Simulator" dengan width 25%
- Menyesuaikan width kolom lain untuk proporsi yang seimbang

#### Update DataTable Configuration
- Menambahkan kolom baru di posisi index 2
- Menyesuaikan mapping data untuk semua kolom

## Logika Category Simulator

### Category 1 - CT Simulator
- Mengambil data dari tabel `medis.tb_ctSimulatorDokter`
- Menampilkan format: "CT - [deskripsi]"
- JOIN berdasarkan `id_ctsim` dan `category = "1"`

### Category 2 - Simulator Konvensional  
- Mengambil data dari tabel `medis.tb_simulatorInformationDr`
- Menampilkan format: "SK - [diagnosa]"
- JOIN berdasarkan `id_ctsim` dan `category = "2"`

### Default
- Jika tidak ada data simulator atau category tidak dikenali
- Menampilkan: "Tidak ada catatan"

## Struktur Tabel Setelah Perubahan

| No | Lokasi | Catatan Simulator | Oleh | Waktu | Aksi |
|----|--------|------------------|------|-------|------|
| 1  | kepala | CT - Deskripsi CT | User | DateTime | Buttons |
| 2  | dada   | SK - Diagnosa SK  | User | DateTime | Buttons |

## File yang Dimodifikasi
1. `application/models/radioterapi/LaporanRadiasiEksternaModel.php`
2. `application/controllers/radioterapi/LaporanRadiasiEksterna.php`
3. `application/views/Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna.php`

## Keuntungan Perubahan
1. **Informasi Lengkap**: User dapat melihat catatan simulator langsung di tabel
2. **Traceability**: Mudah melacak data simulator yang digunakan
3. **Efisiensi**: Tidak perlu membuka detail untuk melihat catatan simulator
4. **Konsistensi**: Format yang konsisten untuk CT dan Simulator Konvensional
5. **User Experience**: Informasi yang lebih komprehensif dalam satu tampilan

## Catatan Teknis
- JOIN menggunakan LEFT JOIN untuk menghindari data hilang
- CASE statement memastikan format yang konsisten
- Category 1 = CT Simulator, Category 2 = Simulator Konvensional
- Data tetap aman jika tidak ada referensi simulator
