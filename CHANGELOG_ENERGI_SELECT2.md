# Changelog: Implementasi Select2 untuk Field Energi

## <PERSON>kas<PERSON>an
<PERSON> field input "energi" pada modal detail radiasi eksterna menjadi komponen Select2 dengan data dari tabel `db_master.variabel`.

## File yang Dimodifikasi

### 1. Controller: `application/controllers/radioterapi/LaporanRadiasiEksterna.php`

#### Perubahan:
- **<PERSON>s 97**: Mengubah `$field->energi` menjadi `$field->energi_display` di method `get_detail_data()`
- **Baris 332-348**: Menambahkan method `get_energi_options()` untuk endpoint AJAX Select2

```php
/**
 * Get data energi untuk Select2 dropdown
 */
public function get_energi_options()
{
    $search = $this->input->get('q');
    $id = $this->input->get('id');
    $data = $this->model->get_energi_options($search, $id);
    
    $results = array();
    foreach ($data as $row) {
        $results[] = array(
            'id' => $row->id_variabel,
            'text' => $row->variabel
        );
    }
    
    echo json_encode(array('results' => $results));
}
```

### 2. Model: `application/models/radioterapi/LaporanRadiasiEksternaModel.php`

#### Perubahan:
- **Baris 94-127**: Mengupdate method `_get_datatables_query_detail()` untuk JOIN dengan tabel `db_master.variabel`
- **Baris 315-337**: Menambahkan method `get_energi_options()` untuk mengambil data energi

```php
private function _get_datatables_query_detail($id_radiasi)
{
    $this->db->select([
        'medis.radiasi_eksterna_detail.*',
        'COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi) as energi_display'
    ]);
    $this->db->from($this->table_detail);
    $this->db->join('db_master.variabel', 'db_master.variabel.id_variabel = medis.radiasi_eksterna_detail.energi AND db_master.variabel.id_referensi = 1862', 'left');
    // ... rest of the method
}

public function get_energi_options($search = '', $id = null)
{
    $this->db->select('id_variabel, variabel');
    $this->db->from('db_master.variabel');
    $this->db->where('id_referensi', 1862);
    $this->db->where('status', 1);
    
    if (!empty($search)) {
        $this->db->like('variabel', $search);
    }
    
    if (!empty($id)) {
        $this->db->where('id_variabel', $id);
    }
    
    $this->db->order_by('seq ASC, variabel ASC');
    $query = $this->db->get();
    
    return $query->result();
}
```

### 3. View: `application/views/Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna.php`

#### Perubahan:

**A. Form Input Detail (Baris 386-393):**
```html
<!-- SEBELUM -->
<input type="text" class="form-control" id="energi" name="energi" required placeholder="Contoh: 6MV">

<!-- SESUDAH -->
<select class="form-control select2-energi" id="energi" name="energi" required style="width: 100%;">
    <option value="">-- Pilih Energi --</option>
</select>
```

**B. Form Edit Detail (Baris 285-292):**
```html
<!-- SEBELUM -->
<input type="text" class="form-control" id="edit_energi" name="energi" required>

<!-- SESUDAH -->
<select class="form-control select2-energi-edit" id="edit_energi" name="energi" required style="width: 100%;">
    <option value="">-- Pilih Energi --</option>
</select>
```

**C. JavaScript Inisialisasi Select2 (Baris 476-517):**
```javascript
// Inisialisasi select2 untuk dropdown energi di modal detail
$('.select2-energi').select2({
    placeholder: "-- Pilih Energi --",
    allowClear: true,
    dropdownParent: $('#modal-detail'),
    ajax: {
        url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_energi_options') ?>',
        dataType: 'json',
        delay: 250,
        data: function(params) {
            return {
                q: params.term
            };
        },
        processResults: function(data) {
            return data;
        },
        cache: true
    }
});

// Inisialisasi select2 untuk dropdown energi di modal edit detail
$('.select2-energi-edit').select2({
    placeholder: "-- Pilih Energi --",
    allowClear: true,
    dropdownParent: $('#modal-edit-detail'),
    ajax: {
        url: '<?= base_url('radioterapi/LaporanRadiasiEksterna/get_energi_options') ?>',
        dataType: 'json',
        delay: 250,
        data: function(params) {
            return {
                q: params.term
            };
        },
        processResults: function(data) {
            return data;
        },
        cache: true
    }
});
```

**D. Handler Edit Detail (Baris 777-830):**
- Mengupdate handler untuk mengatur nilai Select2 dengan benar saat edit
- Menambahkan AJAX call untuk mendapatkan text dari ID variabel

**E. Handler Reset Form (Baris 894-911, 896-900):**
- Menambahkan reset untuk Select2 energi saat modal ditutup
- Menambahkan reset untuk tombol reset form

## Spesifikasi Implementasi

### Data Source:
- **Tabel**: `db_master.variabel`
- **Kondisi WHERE**: `id_referensi = 1862 AND status = 1`
- **Value yang disimpan**: `id_variabel` (integer)
- **Text yang ditampilkan**: field `variabel` dari tabel

### Endpoint API:
- **URL**: `/radioterapi/LaporanRadiasiEksterna/get_energi_options`
- **Method**: GET
- **Parameters**: 
  - `q` (optional): untuk search/filter
  - `id` (optional): untuk mendapatkan data spesifik berdasarkan ID
- **Response Format**: 
```json
{
    "results": [
        {"id": "6294", "text": "6 MV"},
        {"id": "6295", "text": "10 MV"},
        {"id": "6296", "text": "6 MV FFF"}
    ]
}
```

### Fitur yang Diimplementasi:
1. ✅ Select2 dropdown dengan AJAX loading
2. ✅ Search/filter data energi
3. ✅ Simpan `id_variabel` sebagai value
4. ✅ Tampilkan nama energi di datatable (bukan ID)
5. ✅ Support edit data existing
6. ✅ Reset form yang proper
7. ✅ Dropdown parent yang benar untuk modal

## Testing
- Endpoint dapat diakses di: `http://localhost:8000/radioterapi/LaporanRadiasiEksterna/get_energi_options?q=MV`
- Form input dan edit sudah menggunakan Select2
- Datatable menampilkan nama energi yang readable

## Catatan
- Field energi di database tetap menyimpan `id_variabel` (integer)
- Backward compatibility terjaga dengan menggunakan COALESCE di query
- Select2 menggunakan AJAX untuk performa yang lebih baik
- Implementasi mengikuti pattern yang sudah ada di aplikasi
