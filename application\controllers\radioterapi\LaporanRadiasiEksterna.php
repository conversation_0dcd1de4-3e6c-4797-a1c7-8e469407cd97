<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanRadiasiEksterna extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        // Validasi akses - hanya radiografer (profesi = 8) yang boleh akses
        if (!in_array(8, $this->session->userdata('akses'))) {
            redirect('login');
        }

        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('radioterapi/LaporanRadiasiEksternaModel', 'model');
        $this->load->model('MasterModel');
    }

    /**
     * Validasi apakah user adalah radiografer
     */
    private function _validate_radiografer()
    {
        if ($this->session->userdata('profesi') != 8) {
            echo json_encode(['status' => FALSE, 'message' => 'Aks<PERSON> ditolak. Hanya radiografer yang dapat melakukan input.']);
            exit;
        }
    }

    public function index()
    {
        // nomr digunakan untuk mengambil list data, nokun untuk proses simpan data baru
        $data['nomr'] = $this->input->get('nomr');
        $data['nokun'] = $this->input->get('nokun');
        $data['simulator_data'] = $this->model->get_simulator_data($data['nomr']);
        $this->load->view('Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna', $data);
    }

    public function get_data()
    {
        $nomr = $this->input->get('nomr');
        $list = $this->model->get_datatables($nomr);
        $data = array();
        $no = $_GET['start'];
        foreach ($list as $field) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $field->lokasi;
            $row[] = $field->catatan_simulator;
            $row[] = $this->MasterModel->getPenguna($field->oleh);
            $row[] = date('d-m-Y H:i:s', strtotime($field->waktu));

            // Tombol aksi
            $buttons = '';
            if ($this->session->userdata('profesi') == 8) {
                $buttons .= '<button type="button" class="btn btn-info btn-sm detail-btn" data-id="' . $field->id . '" title="Lihat Detail"><i class="fa fa-list"></i></button>';

                // Tombol edit dan hapus hanya jika user yang login adalah yang membuat data
                if ($field->oleh == $this->session->userdata('id')) {
                    $buttons .= ' <button type="button" class="btn btn-primary btn-sm edit-lokasi-btn" data-id="' . $field->id . '" title="Edit Lokasi"><i class="fa fa-edit"></i></button>';
                    $buttons .= ' <button type="button" class="btn btn-danger btn-sm delete-btn" data-id="' . $field->id . '" title="Hapus"><i class="fa fa-trash"></i></button>';
                }
            } else {
                $buttons = '<button type="button" class="btn btn-info btn-sm detail-btn" data-id="' . $field->id . '" title="Lihat Detail"><i class="fa fa-list"></i></button>';
            }
            $row[] = $buttons;
            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all($nomr),
            "recordsFiltered" => $this->model->count_filtered($nomr),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function get_detail_data()
    {
        $id_radiasi = $this->input->get('id_radiasi');
        $list = $this->model->get_datatables_detail($id_radiasi);
        $data = array();
        $no = $_GET['start'];
        foreach ($list as $field) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = date('d-m-Y', strtotime($field->tanggal));
            $row[] = $field->waktu;
            $row[] = $field->energi_display; // Gunakan energi_display yang sudah di-JOIN
            $row[] = $field->ssd_sad;
            $row[] = $field->dosis_fraksi;
            $row[] = $this->MasterModel->getPenguna($field->oleh);

            // Tombol aksi untuk detail
            $buttons_detail = '';
            if ($this->session->userdata('profesi') == 8 && $field->oleh == $this->session->userdata('id')) {
                $buttons_detail .= '<button type="button" class="btn btn-primary btn-sm edit-detail-btn" data-id="' . $field->id . '" title="Edit"><i class="fa fa-edit"></i></button>';
                $buttons_detail .= ' <button type="button" class="btn btn-danger btn-sm delete-detail-btn" data-id="' . $field->id . '" title="Hapus"><i class="fa fa-trash"></i></button>';
            }
            $row[] = $buttons_detail;

            $data[] = $row;
        }

        $output = array(
            "draw" => $_GET['draw'],
            "recordsTotal" => $this->model->count_all_detail($id_radiasi),
            "recordsFiltered" => $this->model->count_filtered_detail($id_radiasi),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function save()
    {
        // Validasi hanya radiografer yang boleh input
        $this->_validate_radiografer();

        // Validasi input
        if (empty($this->input->post('nokun')) || empty($this->input->post('lokasi')) || empty($this->input->post('id_ctsim'))) {
            echo json_encode(['status' => FALSE, 'message' => 'Data tidak lengkap. Lokasi radiasi dan catatan simulator wajib diisi.']);
            return;
        }

        $data = array(
            'nokun' => $this->input->post('nokun'),
            'lokasi' => $this->input->post('lokasi'),
            'id_ctsim' => $this->input->post('id_ctsim') ? $this->input->post('id_ctsim') : null,
            'category' => $this->input->post('category') ? $this->input->post('category') : null,
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );

        $result = $this->model->save($data);
        if ($result) {
            // Ambil data yang baru saja disimpan
            $newData = $this->model->get_by_id($result);
            echo json_encode(['status' => TRUE, 'message' => 'Data berhasil disimpan', 'data' => $newData]);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menyimpan data']);
        }
    }

    public function save_detail()
    {
        // Validasi hanya radiografer yang boleh input
        $this->_validate_radiografer();

        // Validasi input
        $required_fields = ['id_radiasi', 'tanggal', 'energi', 'ssd_sad', 'dosis_fraksi'];
        foreach ($required_fields as $field) {
            if (empty($this->input->post($field))) {
                echo json_encode(['status' => FALSE, 'message' => 'Data tidak lengkap']);
                return;
            }
        }

        $data = array(
            'id_radiasi' => $this->input->post('id_radiasi'),
            'tanggal' => $this->input->post('tanggal'),
            'energi' => $this->input->post('energi'),
            'ssd_sad' => $this->input->post('ssd_sad'),
            'dosis_fraksi' => $this->input->post('dosis_fraksi'),
            'oleh' => $this->session->userdata('id'),
            'status' => 1,
        );

        $result = $this->model->save_detail($data);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Detail berhasil disimpan']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menyimpan detail']);
        }
    }

    /**
     * Get data radiasi by ID untuk modal
     */
    public function get_radiasi_by_id()
    {
        $id = $this->input->get('id');
        $data = $this->model->get_by_id($id);
        echo json_encode($data);
    }

    public function update()
    {
        $this->_validate_radiografer();

        $id = $this->input->post('id');
        
        // Cek kepemilikan data sebelum update
        $radiasi = $this->model->get_by_id($id);
        if (!$radiasi || $radiasi->oleh != $this->session->userdata('id')) {
            echo json_encode(['status' => FALSE, 'message' => 'Akses ditolak. Anda tidak memiliki hak untuk mengedit data ini.']);
            return;
        }

        // Validasi input wajib
        if (empty($this->input->post('lokasi')) || empty($this->input->post('id_ctsim'))) {
            echo json_encode(['status' => FALSE, 'message' => 'Lokasi radiasi dan catatan simulator wajib diisi.']);
            return;
        }

        $data = array(
            'lokasi' => $this->input->post('lokasi'),
            'id_ctsim' => $this->input->post('id_ctsim'),
            'category' => $this->input->post('category'),
            'waktu' => date('Y-m-d H:i:s'), // Tambahkan timestamp update
        );

        $result = $this->model->update($id, $data);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Data berhasil diperbarui']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal memperbarui data']);
        }
    }

    public function get_detail_by_id()
    {
        $id = $this->input->get('id');
        $data = $this->model->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function update_detail()
    {
        $this->_validate_radiografer();

        $id = $this->input->post('id_detail');

        // Cek kepemilikan data sebelum update
        $detail = $this->model->get_detail_by_id($id);
        if (!$detail || $detail->oleh != $this->session->userdata('id')) {
            echo json_encode(['status' => FALSE, 'message' => 'Akses ditolak. Anda tidak memiliki hak untuk mengedit data ini.']);
            return;
        }

        $data = array(
            'tanggal' => $this->input->post('tanggal'),
            'energi' => $this->input->post('energi'),
            'ssd_sad' => $this->input->post('ssd_sad'),
            'dosis_fraksi' => $this->input->post('dosis_fraksi'),
            'waktu' => date('Y-m-d H:i:s'), // Tambahkan timestamp update
        );

        $result = $this->model->update_detail($id, $data);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Detail berhasil diperbarui']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal memperbarui detail']);
        }
    }

    public function delete($id)
    {
        $this->_validate_radiografer();
        
        // Cek kepemilikan data
        $radiasi = $this->model->get_by_id($id);
        if ($radiasi->oleh != $this->session->userdata('id')) {
            echo json_encode(['status' => FALSE, 'message' => 'Anda tidak memiliki hak untuk menghapus data ini.']);
            return;
        }

        $result = $this->model->delete($id);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Data berhasil dihapus']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menghapus data']);
        }
    }

    public function delete_detail($id)
    {
        $this->_validate_radiografer();

        // Cek kepemilikan data
        $detail = $this->model->get_detail_by_id($id);
        if ($detail->oleh != $this->session->userdata('id')) {
            echo json_encode(['status' => FALSE, 'message' => 'Anda tidak memiliki hak untuk menghapus data ini.']);
            return;
        }

        $result = $this->model->delete_detail($id);
        if ($result) {
            echo json_encode(['status' => TRUE, 'message' => 'Detail berhasil dihapus']);
        } else {
            echo json_encode(['status' => FALSE, 'message' => 'Gagal menghapus detail']);
        }
    }

    /**
     * Method untuk debugging - cek struktur tabel
     */
    public function check_table()
    {
        $structure = $this->model->check_table_structure();
        echo '<pre>';
        print_r($structure);
        echo '</pre>';
    }

    /**
     * Method untuk menambahkan kolom yang hilang
     */
    public function add_columns()
    {
        try {
            $result = $this->model->add_missing_columns();
            if ($result) {
                echo "Kolom berhasil ditambahkan!";
            } else {
                echo "Gagal menambahkan kolom!";
            }
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage();
        }
    }

    /**
     * Get data energi untuk Select2 dropdown
     */
    public function get_energi_options()
    {
        $search = $this->input->get('q');
        $id = $this->input->get('id');
        $data = $this->model->get_energi_options($search, $id);

        $results = array();
        foreach ($data as $row) {
            $results[] = array(
                'id' => $row->id_variabel,
                'text' => $row->variabel
            );
        }

        echo json_encode(array('results' => $results));
    }
}
