# <PERSON><PERSON><PERSON> Edit dan Implementasi Select2

## Perubahan yang Diterapkan

### 1. **Modal Edit Lokasi Radiasi**

#### <PERSON>bahan <PERSON>
- **Sebelumnya**: "Edit Lokasi Radiasi"
- **Sekarang**: "Edit Lokasi Radiasi & Catatan Simulator"

#### Penambahan Field Baru
- Ditambahkan field "Catatan Simulator" dengan dropdown select2
- Ditambahkan hidden fields untuk `id_ctsim` dan `category`
- Menggunakan styling yang sama dengan form utama (lokasi-radiasi-group dan simulator-reference-group)

#### Struktur Modal Baru
```html
<form id="form-edit-lokasi">
    <input type="hidden" name="id" id="edit_id_radiasi">
    <input type="hidden" name="id_ctsim" id="edit_id_ctsim">
    <input type="hidden" name="category" id="edit_category">
    
    <!-- Field Lokasi Radiasi -->
    <div class="form-group lokasi-radiasi-group">
        <label>Lokasi Radiasi *</label>
        <input type="text" id="edit_lokasi" name="lokasi" required>
    </div>
    
    <!-- Field Catatan Simulator -->
    <div class="form-group simulator-reference-group">
        <label>Catatan Simulator *</label>
        <select id="edit_simulator_dropdown" name="simulator_dropdown" required>
            <!-- Options dari simulator_data -->
        </select>
    </div>
</form>
```

### 2. **Implementasi Select2**

#### Form Utama
- Mengubah `id="simulator_dropdown"` menjadi `class="select2-simulator"`
- Menambahkan `name="simulator_dropdown"` untuk form submission

#### Modal Edit
- Menggunakan `id="edit_simulator_dropdown"` dengan select2
- Konfigurasi khusus dengan `dropdownParent: $('#modal-edit-lokasi')`

#### Inisialisasi JavaScript
```javascript
// Form utama
$('.select2-simulator').select2({
    placeholder: "Pilih catatan simulator",
    allowClear: true
});

// Modal edit
$('#edit_simulator_dropdown').select2({
    placeholder: "Pilih catatan simulator",
    allowClear: true,
    dropdownParent: $('#modal-edit-lokasi')
});
```

### 3. **Handler JavaScript Baru**

#### Handler Dropdown Modal Edit
- Menangani perubahan dropdown simulator di modal edit
- Mengisi hidden fields `edit_id_ctsim` dan `edit_category`

#### Handler Load Data Edit
- Mengambil data `id_ctsim` dan `category` dari response
- Mencari dan memilih option yang sesuai di dropdown
- Menggunakan filter berdasarkan `data-id` dan `data-category`

#### Handler Reset Form
- Menggunakan class selector `.select2-simulator` untuk reset
- Memastikan semua dropdown select2 ter-reset dengan benar

### 4. **Perubahan Controller**

#### Method `update()`
- Ditambahkan validasi wajib untuk `lokasi` dan `id_ctsim`
- Menambahkan field `id_ctsim` dan `category` ke data update
- Error message yang lebih informatif

```php
// Validasi input wajib
if (empty($this->input->post('lokasi')) || empty($this->input->post('id_ctsim'))) {
    echo json_encode(['status' => FALSE, 'message' => 'Lokasi radiasi dan catatan simulator wajib diisi.']);
    return;
}

$data = array(
    'lokasi' => $this->input->post('lokasi'),
    'id_ctsim' => $this->input->post('id_ctsim'),
    'category' => $this->input->post('category'),
    'waktu' => date('Y-m-d H:i:s')
);
```

## Fitur Baru yang Tersedia

### 1. **Edit Catatan Simulator**
- User dapat mengubah catatan simulator saat edit lokasi radiasi
- Dropdown menampilkan semua data simulator yang tersedia
- Validasi wajib untuk kedua field

### 2. **Select2 Implementation**
- Dropdown yang lebih user-friendly dengan search functionality
- Placeholder yang informatif
- Clear button untuk reset pilihan

### 3. **Data Consistency**
- Saat load data edit, dropdown otomatis menampilkan pilihan yang sesuai
- Hidden fields memastikan data referensi tersimpan dengan benar

## File yang Dimodifikasi
1. `application/views/Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna.php`
2. `application/controllers/radioterapi/LaporanRadiasiEksterna.php`

## Keuntungan Perubahan
1. **Konsistensi Data**: Lokasi dan catatan simulator dapat diubah bersamaan
2. **User Experience**: Select2 memberikan pengalaman yang lebih baik
3. **Validasi Lengkap**: Kedua field wajib diisi saat insert maupun update
4. **Fleksibilitas**: User dapat mengubah referensi simulator jika diperlukan
5. **Visual Consistency**: Styling yang sama antara form utama dan modal edit
